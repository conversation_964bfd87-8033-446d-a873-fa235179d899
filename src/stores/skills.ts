// ยังไม่เสดดด test test
import { defineStore } from 'pinia';
import type { Skill } from 'src/types/models';

// Mock data for skills (moved from SkillManagementPage.vue)
const mockSkills: Skill[] = [
  {
    id: 1,
    competencyId: 1,
    name: 'การวางแผนโครงการ',
    description: 'ความสามารถในการวางแผนขั้นตอนการดำเนินงานโครงการ',
    type: 'Technical Skill',
    career: 'บุคลากร', // Changed career to match 'general-staff' tab
  },
  {
    id: 2,
    competencyId: 1,
    name: 'การติดตามผลการดำเนินงาน',
    description: 'ทักษะในการควบคุมและติดตามความคืบหน้าของโครงการ',
    type: 'Management Skill',
    career: 'บุคลากร', // Changed career to match 'general-staff' tab
  },
  {
    id: 3,
    competencyId: 2,
    name: 'การนำเสนอต่อผู้บริหาร',
    description: 'ความสามารถในการนำเสนอข้อมูลและผลงานต่อผู้บริหารอย่างมีประสิทธิภาพ',
    type: 'Communication Skill',
    career: 'ผู้บริหาร', // Changed career to match 'general-admin' or 'admin' tab
  },
  {
    id: 4,
    competencyId: 2,
    name: 'การเขียนรายงาน',
    description: 'ทักษะในการเขียนรายงานที่ชัดเจนและเข้าใจง่าย',
    type: 'Communication Skill',
    career: 'บุคลากร', // Changed career to match 'general-staff' tab
  },
  {
    id: 5,
    competencyId: 3,
    name: 'การวิเคราะห์ข้อมูล',
    description: 'ความสามารถในการประมวลผลและวิเคราะห์ข้อมูลเชิงตัวเลข',
    type: 'Analytical Skill',
    career: 'สายวิชาการ', // Changed career to match 'academic' tab
  },
  {
    id: 6,
    competencyId: 3,
    name: 'การแก้ไขปัญหาเชิงระบบ',
    description: 'ทักษะในการหาสาเหตุและแนวทางแก้ไขปัญหาอย่างเป็นระบบ',
    type: 'Problem Solving Skill',
    career: 'สายสนับสนุนวิชาการ', // Changed career to match 'support' tab
  },
  {
    id: 7,
    competencyId: 4,
    name: 'การสร้างแรงบันดาลใจ',
    description: 'ความสามารถในการสร้างแรงจูงใจและกระตุ้นทีมงาน',
    type: 'Leadership Skill',
    career: 'ผู้บริหาร', // Changed career to match 'general-admin' or 'admin' tab
  },
  {
    id: 8,
    competencyId: 5,
    name: 'การใช้งาน Microsoft Office',
    description: 'ทักษะในการใช้โปรแกรม Word, Excel, PowerPoint อย่างชำนาญ',
    type: 'Technical Skill',
    career: 'บุคลากร', // Changed career to match 'general-staff' tab
  },
];

export const useSkillStore = defineStore('skills', {
  state: () => ({
    skills: mockSkills, // Initial state with mock data
  }),
  getters: {
    // You can add getters here if needed, e.g., filtered skills
  },
  actions: {
    addSkill(skill: Skill) {
      // Assign a new ID (this logic should ideally be handled by backend)
      const newId = Math.max(0, ...this.skills.map((r) => r.id)) + 1;
      this.skills.push({ ...skill, id: newId });
    },
    editSkill(updatedSkill: Skill) {
      const index = this.skills.findIndex((item) => item.id === updatedSkill.id);
      if (index !== -1) {
        this.skills[index] = { ...updatedSkill };
      }
    },
    deleteSkill(skillId: number) {
      this.skills = this.skills.filter((item) => item.id !== skillId);
    },
    // Action to load skills from backend (placeholder)
    fetchSkills() {
      // TODO: Implement fetching data from backend
      console.log('Fetching skills from backend...');
      // Example: const response = await api.get('/skills');
      // this.skills = response.data;
    },
  },
});
